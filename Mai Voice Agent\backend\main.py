"""
Mai Voice Agent Backend
Organized FastAPI application with modular structure
"""

import logging
import asyncio
import os
from contextlib import asynccontextmanager
from pathlib import Path

import uvicorn
from fastapi import Fast<PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse

from config import settings, validate_configuration
from routes import router
from ai_handlers import ai_handler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Background tasks
async def cleanup_task():
    """Background task to clean up inactive sessions"""
    while True:
        try:
            ai_handler.cleanup_inactive_sessions()
            await asyncio.sleep(300)  # Run every 5 minutes
        except Exception as e:
            logger.error(f"Cleanup task error: {e}")
            await asyncio.sleep(60)  # Retry after 1 minute on error

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("🚀 Starting Mai Voice Agent...")

    # Validate configuration
    if not validate_configuration():
        raise RuntimeError("Configuration validation failed")

    # Start background tasks
    cleanup_task_handle = asyncio.create_task(cleanup_task())

    logger.info("✅ Mai Voice Agent started successfully")

    yield

    # Shutdown
    logger.info("🛑 Shutting down Mai Voice Agent...")
    cleanup_task_handle.cancel()
    try:
        await cleanup_task_handle
    except asyncio.CancelledError:
        pass
    logger.info("✅ Mai Voice Agent shutdown complete")

# Create FastAPI application
app = FastAPI(
    title="Mai Voice Agent",
    description="AI Voice Assistant for Critical Future",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(router, prefix="/api")

# Serve static files (frontend)
# Get the correct path to frontend directory
current_dir = Path(__file__).parent
frontend_dir = current_dir.parent / "frontend"

try:
    if frontend_dir.exists():
        # Mount static files at root level for direct access
        app.mount("/", StaticFiles(directory=str(frontend_dir), html=True), name="frontend")
        logger.info(f"Frontend files mounted from: {frontend_dir}")
    else:
        logger.warning(f"Frontend directory not found at: {frontend_dir}")
except Exception as e:
    logger.warning(f"Could not mount frontend files: {e}")

# Static files are now mounted at root level, so no need for a separate root endpoint

# Health check endpoint (also available at root level)
@app.get("/health")
async def health_check():
    """Root level health check"""
    return {
        "status": "healthy",
        "service": "mai-voice-agent",
        "version": "1.0.0",
        "message": "Mai Voice Agent is running"
    }

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request: Request, exc: HTTPException):
    """Handle 404 errors"""
    return JSONResponse(
        status_code=404,
        content={"error": "Not found", "detail": "The requested resource was not found"}
    )

@app.exception_handler(500)
async def internal_error_handler(request: Request, exc: Exception):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {exc}")
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "detail": "An unexpected error occurred"}
    )

# Main entry point
if __name__ == "__main__":
    logger.info(f"Starting Mai Voice Agent on port {settings.port}")

    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info"
    )
