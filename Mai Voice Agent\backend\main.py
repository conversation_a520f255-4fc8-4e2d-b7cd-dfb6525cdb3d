"""
Mai Voice Agent Backend
Organized FastAPI application with modular structure
"""

import logging
import asyncio
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse

import base64
import numpy as np
from io import BytesIO
from PIL import Image
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from config import settings, validate_configuration
from routes import router
from ai_handlers import ai_handler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Background tasks
async def cleanup_task():
    """Background task to clean up inactive sessions"""
    while True:
        try:
            ai_handler.cleanup_inactive_sessions()
            await asyncio.sleep(300)  # Run every 5 minutes
        except Exception as e:
            logger.error(f"Cleanup task error: {e}")
            await asyncio.sleep(60)  # Retry after 1 minute on error

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("🚀 Starting Mai Voice Agent...")

    # Validate configuration
    if not validate_configuration():
        raise RuntimeError("Configuration validation failed")

    # Start background tasks
    cleanup_task_handle = asyncio.create_task(cleanup_task())

    logger.info("✅ Mai Voice Agent started successfully")

    yield

    # Shutdown
    logger.info("🛑 Shutting down Mai Voice Agent...")
    cleanup_task_handle.cancel()
    try:
        await cleanup_task_handle
    except asyncio.CancelledError:
        pass
    logger.info("✅ Mai Voice Agent shutdown complete")

# Create FastAPI application
app = FastAPI(
    title="Mai Voice Agent",
    description="AI Voice Assistant for Critical Future",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(router, prefix="/api")

# Serve static files (frontend)
try:
    app.mount("/static", StaticFiles(directory="frontend"), name="static")
except Exception as e:
    logger.warning(f"Could not mount static files: {e}")

# Root endpoint - serve frontend
@app.get("/")
async def root():
    """Serve the frontend application"""
    try:
        return FileResponse("frontend/index.html")
    except Exception as e:
        logger.error(f"Error serving frontend: {e}")
        return {"message": "Mai Voice Agent API", "status": "running", "docs": "/docs"}

# Health check endpoint (also available at root level)
@app.get("/health")
async def health_check():
    """Root level health check"""
    return {
        "status": "healthy",
        "service": "mai-voice-agent",
        "version": "1.0.0",
        "message": "Mai Voice Agent is running"
    }

# Error handlers
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse

@app.exception_handler(404)
async def not_found_handler(request: Request, exc: HTTPException):
    """Handle 404 errors"""
    return JSONResponse(
        status_code=404,
        content={"error": "Not found", "detail": "The requested resource was not found"}
    )

@app.exception_handler(500)
async def internal_error_handler(request: Request, exc: Exception):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {exc}")
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "detail": "An unexpected error occurred"}
    )

# Main entry point
if __name__ == "__main__":
    logger.info(f"Starting Mai Voice Agent on port {settings.port}")

    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info"
    )

# End of main.py - organized backend structure
# The following functions were incorrectly placed outside of any class or function.
# They are utility functions that should be part of a module or a class.
# For now, they are commented out as they are not directly used in this file's current structure
# and their placement was causing a syntax error.
# If they are intended to be used, they should be moved to a proper utility module (e.g., `utils.py`)
# or integrated into an existing class.
def encode_image(data: np.ndarray) -> dict:
    """Encode image data to send to Gemini"""
    with BytesIO() as output_bytes:
        pil_image = Image.fromarray(data)
        pil_image.save(output_bytes, "JPEG")
        bytes_data = output_bytes.getvalue()
    base64_str = str(base64.b64encode(bytes_data), "utf-8")
    return {"mime_type": "image/jpeg", "data": base64_str}

def encode_file_content(file_content: bytes, mime_type: str) -> dict:
    """Encode file content to send to Gemini"""
    base64_str = base64.b64encode(file_content).decode("UTF-8")
    return {"mime_type": mime_type, "data": base64_str}

# Email functionality
async def send_email(to_email: str, subject: str, body: str, is_html: bool = False):
    """Send email using configured SMTP settings"""
    try:
        msg = MIMEMultipart()
        msg['From'] = settings.email_address
        msg['To'] = to_email
        msg['Subject'] = subject
        
        if is_html:
            msg.attach(MIMEText(body, 'html'))
        else:
            msg.attach(MIMEText(body, 'plain'))
        
        server = smtplib.SMTP(settings.smtp_server, settings.smtp_port)
        server.starttls()
        server.login(settings.email_address, settings.email_password)
        text = msg.as_string()
        server.sendmail(settings.email_address, to_email, text)
        server.quit()
        
        logger.info(f"Email sent successfully to {to_email}")
        return True
    except Exception as e:
        logger.error(f"Failed to send email to {to_email}: {e}")
        return False

async def send_follow_up_emails(caller_info: dict, conversation_summary: str):
    """Send follow-up emails to caller and Critical Future team"""
    try:
        # Email to caller
        caller_subject = "Thank you for contacting Critical Future"
        caller_body = f"""Dear {caller_info.get('name', 'Valued Contact')},

Thank you for reaching out to Critical Future. We've received your enquiry and wanted to confirm the details:

Company: {caller_info.get('company', 'Not specified')}
Email: {caller_info.get('email', 'Not provided')}
Phone: {caller_info.get('phone', 'Not provided')}
Enquiry: {caller_info.get('purpose', 'General enquiry')}

Summary of our conversation:
{conversation_summary}

One of our team members will be in touch with you shortly to discuss how we can help with your AI and strategy needs.

Best regards,
Mai
AI Assistant for Critical Future
"""
        
        # Email to Critical Future team
        team_subject = f"New Lead: {caller_info.get('name', 'Unknown')} from {caller_info.get('company', 'Unknown Company')}"
        team_body = f"""New lead captured by Mai:

CONTACT DETAILS:
- Name: {caller_info.get('name', 'Not provided')}
- Company: {caller_info.get('company', 'Not provided')}
- Email: {caller_info.get('email', 'Not provided')}
- Phone: {caller_info.get('phone', 'Not provided')}
- Purpose: {caller_info.get('purpose', 'Not specified')}

CONVERSATION SUMMARY:
{conversation_summary}

Please follow up with this lead promptly.

Best regards,
Mai Voice Agent
"""
        
        # Send emails
        if caller_info.get('email'):
            await send_email(caller_info['email'], caller_subject, caller_body)
        
        await send_email(settings.email_address, team_subject, team_body)
        
        return True
    except Exception as e:
        logger.error(f"Error sending follow-up emails: {e}")
        return False

# Import FastRTC with comprehensive error handling and version compatibility
FASTRTC_AVAILABLE = False
FASTRTC_ERROR = None

try:
    # First test if aiortc has the required components
    from aiortc import AudioStreamTrack, VideoStreamTrack, RTCPeerConnection
    logger.info("✅ aiortc components available")

    # Then try to import FastRTC
    from fastrtc import (
        AsyncStreamHandler,
        AsyncAudioVideoStreamHandler,
        Stream,
        get_cloudflare_turn_credentials_async,
        wait_for_item,
    )

    # Test FastRTC components
    from fastrtc.tracks import EmitType, StreamHandler

    FASTRTC_AVAILABLE = True
    logger.info("✅ FastRTC imported successfully with all components")

except ImportError as e:
    FASTRTC_ERROR = str(e)
    logger.error(f"❌ FastRTC/aiortc compatibility issue: {e}")

    if "AudioStreamTrack" in str(e):
        logger.error("🔧 This is a known aiortc version compatibility issue")
        logger.error("💡 Try: aiortc==1.6.0 or use text-only mode")

    # Create comprehensive fallback classes
    class AsyncStreamHandler:
        def __init__(self, expected_layout="mono", output_sample_rate=24000, input_sample_rate=16000):
            self.expected_layout = expected_layout
            self.output_sample_rate = output_sample_rate
            self.input_sample_rate = input_sample_rate
            self.phone_mode = False
            self.latest_args = None
            logger.warning("Using fallback AsyncStreamHandler")

        async def wait_for_args(self):
            pass

        async def start_up(self):
            logger.info("Fallback handler startup - voice features disabled")

        async def receive(self, frame):
            pass

        async def emit(self):
            return None

        def shutdown(self):
            pass

        def copy(self):
            return AsyncStreamHandler(self.expected_layout, self.output_sample_rate)

    class AsyncAudioVideoStreamHandler(AsyncStreamHandler):
        def __init__(self, expected_layout="mono", output_sample_rate=24000, input_sample_rate=16000):
            super().__init__(expected_layout, output_sample_rate, input_sample_rate)
            logger.warning("Using fallback AsyncAudioVideoStreamHandler")

        async def video_receive(self, frame):
            pass

        async def video_emit(self):
            return np.zeros((100, 100, 3), dtype=np.uint8)

        def copy(self):
            return AsyncAudioVideoStreamHandler(self.expected_layout, self.output_sample_rate)

    class Stream:
        def __init__(self, modality="audio", mode="send-receive", handler=None, **kwargs):
            self.modality = modality
            self.mode = mode
            self.handler = handler or AsyncStreamHandler()
            logger.warning("Using fallback Stream implementation")
            logger.info("Voice and video features are disabled - text chat fully functional")

        def mount(self, app, path=""):
            # Add fallback WebRTC endpoints that return proper error messages
            endpoint_path = f"{path}/webrtc/offer" if path else "/webrtc/offer"
            status_path = f"{path}/webrtc/status" if path else "/webrtc/status"

            @app.post(endpoint_path)
            async def webrtc_offer_fallback(request: Request):
                body = await request.json()
                return {
                    "status": "failed",
                    "meta": {
                        "error": "webrtc_not_available",
                        "message": f"WebRTC features are not available: {FASTRTC_ERROR}",
                        "suggestion": "Use text chat mode instead"
                    }
                }

            @app.get(status_path)
            async def webrtc_status():
                return {
                    "available": False,
                    "error": FASTRTC_ERROR,
                    "text_chat_available": True
                }

        def set_input(self, webrtc_id, voice_name, uploaded_files=None):
            logger.warning(f"Stream.set_input() called but WebRTC not available: {FASTRTC_ERROR}")

    async def get_cloudflare_turn_credentials_async():
     return {
        "iceServers": [
            { "urls": ["stun:fr-turn7.xirsys.com"] },
            { "urls": ["stun:stun.l.google.com:19302"] },
            { "urls": ["stun:stun.l.google.com:19302"] },
            { "urls": ["stun:stun1.l.google.com:19302"] },
            {
                "username": "UIuBt8vNm5tNifOx-1ZY-nlw-mNMjhzc_2LsV1Wjpu2ccJ8-u_6wlgw0j7TxEvi6AAAAAGhSBQhNb29tYXJh",
                "credential": "48a4de2c-4bd9-11f0-a9be-6aee953622e2",
                "urls": [
                    "turn:fr-turn7.xirsys.com:80?transport=udp",
                    "turn:fr-turn7.xirsys.com:3478?transport=udp",
                    "turn:fr-turn7.xirsys.com:80?transport=tcp",
                    "turn:fr-turn7.xirsys.com:3478?transport=tcp",
                    "turns:fr-turn7.xirsys.com:443?transport=tcp",
                    "turns:fr-turn7.xirsys.com:5349?transport=tcp"
                ]
            }
        ]
    }

    async def wait_for_item(queue, timeout):
        return None

except Exception as e:
    FASTRTC_ERROR = f"Unexpected error: {str(e)}"
    logger.error(f"❌ Unexpected FastRTC error: {e}")
    FASTRTC_AVAILABLE = False

# Session management
active_sessions = {}

def detect_goodbye(text: str) -> bool:
    """Detect if user is saying goodbye"""
    goodbye_phrases = [
        "bye", "goodbye", "see you later", "talk to you later",
        "i'm done", "that's all", "gotta go", "have to go",
        "end session", "stop session", "finish session", "thank you"
    ]
    text_lower = text.lower().strip()
    return any(phrase in text_lower for phrase in goodbye_phrases)

def extract_contact_info(text: str) -> dict:
    """Extract contact information from conversation text"""
    import re

    contact_info = {}

    # Extract email
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    emails = re.findall(email_pattern, text)
    if emails:
        contact_info['email'] = emails[0]

    # Extract phone (basic pattern)
    phone_pattern = r'(\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4})'
    phones = re.findall(phone_pattern, text)
    if phones:
        contact_info['phone'] = phones[0]

    # Extract name (look for "my name is" or "I'm")
    name_patterns = [
        r'my name is ([A-Za-z\s]+)',
        r"i'm ([A-Za-z\s]+)",
        r'this is ([A-Za-z\s]+)',
        r'call me ([A-Za-z\s]+)'
    ]
    for pattern in name_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        if matches:
            contact_info['name'] = matches[0].strip()
            break

    # Extract company (look for "from" or "at" or "work for")
    company_patterns = [
        r'from ([A-Za-z\s&]+)',
        r'at ([A-Za-z\s&]+)',
        r'work for ([A-Za-z\s&]+)',
        r'company is ([A-Za-z\s&]+)'
    ]
    for pattern in company_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        if matches:
            contact_info['company'] = matches[0].strip()
            break

    return contact_info

class MaiHandler(AsyncStreamHandler):
    """Enhanced handler for Mai Voice Agent with customer service context"""

    def __init__(
        self,
        expected_layout: Literal["mono"] = "mono",
        output_sample_rate: int = 24000,
    ) -> None:
        super().__init__(
            expected_layout,
            output_sample_rate,
            input_sample_rate=16000,
        )
        self.input_queue: asyncio.Queue = asyncio.Queue()
        self.output_queue: asyncio.Queue = asyncio.Queue()
        self.quit: asyncio.Event = asyncio.Event()
        self.session_context = {}
        self.session = None
        self.session_id = None
        self.conversation_log = []

    def copy(self) -> "MaiHandler":
        return MaiHandler(
            expected_layout="mono",
            output_sample_rate=self.output_sample_rate,
        )

    async def start_up(self):
        if not FASTRTC_AVAILABLE:
            logger.warning("FastRTC not available, skipping voice handler setup")
            return

        try:
            # Always use Aoede voice for Mai
            voice_name = "Aoede"

            # Try to get voice from args if available
            if not self.phone_mode:
                try:
                    await self.wait_for_args()
                    if self.latest_args and len(self.latest_args) > 1:
                        voice_name = self.latest_args[1]
                except Exception as args_error:
                    logger.warning(f"Could not get args, using Aoede voice: {args_error}")

            # Validate API key first
            if not settings.gemini_api_key:
                logger.error("GEMINI_API_KEY not provided")
                return

            # Initialize Gemini client
            try:
                if settings.use_vertex_ai and settings.google_cloud_project:
                    self.client = genai.Client(
                        vertexai=True,
                        project=settings.google_cloud_project,
                        location='us-central1'
                    )
                else:
                    self.client = genai.Client(
                        api_key=settings.gemini_api_key,
                    )
                logger.info("Gemini client initialized successfully")
            except Exception as client_error:
                logger.error(f"Failed to initialize Gemini client: {client_error}")
                return

            # Create config with system instructions
            try:
                config = LiveConnectConfig(
                    response_modalities=["AUDIO"],
                    speech_config=SpeechConfig(
                        voice_config=VoiceConfig(
                            prebuilt_voice_config=PrebuiltVoiceConfig(
                                voice_name=voice_name,
                            )
                        )
                    ),
                    generation_config={
                        "temperature": 0.8,
                        "max_output_tokens": 256,
                    },
                    system_instruction={
                        "parts": [{"text": SYSTEM_PROMPT}]
                    }
                )
                logger.info(f"LiveConnectConfig created with system instructions and voice: {voice_name}")
            except Exception as config_error:
                logger.warning(f"Failed to create config with system_instruction, trying without: {config_error}")
                # Fallback config without system_instruction
                try:
                    config = LiveConnectConfig(
                        response_modalities=["AUDIO"],
                        speech_config=SpeechConfig(
                            voice_config=VoiceConfig(
                                prebuilt_voice_config=PrebuiltVoiceConfig(
                                    voice_name=voice_name,
                                )
                            )
                        ),
                        generation_config={
                            "temperature": 0.8,
                            "max_output_tokens": 256,
                        }
                    )
                    logger.info(f"Fallback LiveConnectConfig created with voice: {voice_name}")
                except Exception as fallback_error:
                    logger.error(f"Failed to create fallback LiveConnectConfig: {fallback_error}")
                    return

            logger.info(f"Starting Mai Live session with voice: {voice_name}")

            try:
                async with self.client.aio.live.connect(
                    model="gemini-2.0-flash-exp",
                    config=config
                ) as session:
                    logger.info("Mai Live session established successfully")
                    self.session = session
                    self.session_id = f"mai_voice_{int(time.time())}"
                    active_sessions[self.session_id] = {"type": "voice", "handler": self}

                    # Send comprehensive identity setup messages
                    try:
                        # First, send the full system prompt
                        await session.send({"text": f"SYSTEM INSTRUCTIONS: {SYSTEM_PROMPT}"})

                        # Then send a specific identity reinforcement
                        identity_msg = (
                            f"IMPORTANT: Your identity is: Mai, the AI customer service assistant for Critical Future. "
                            f"Your voice name is {voice_name}. When someone asks 'who are you' or about your identity, "
                            f"always respond: 'Hi there, you're speaking with Mai, the AI assistant for Critical Future. "
                            f"I'm here to help, take notes, and ensure the right person from our team follows up with you directly.' "
                            f"Never say you are created by Google or Gemini. Always say Critical Future."
                        )
                        await session.send({"text": identity_msg})

                        logger.info("System prompt and identity reinforcement sent successfully")
                    except Exception as prompt_error:
                        logger.warning(f"Could not send system messages: {prompt_error}")

                    async for chunk in session.start_stream(
                        stream=self.stream(),
                        mime_type="audio/pcm"
                    ):
                        if self.quit.is_set():
                            logger.info("Quit signal received, breaking stream")
                            break

                        if chunk.data:
                            # Convert audio data to numpy array
                            try:
                                array = np.frombuffer(chunk.data, dtype=np.int16)
                                if not self.quit.is_set() and array.size > 0:
                                    try:
                                        self.output_queue.put_nowait((self.output_sample_rate, array))
                                    except asyncio.QueueFull:
                                        logger.warning("Output queue full, dropping audio frame")
                            except Exception as audio_error:
                                logger.error(f"Error processing audio chunk: {audio_error}")

                        if chunk.text:
                            # Log conversation for follow-up emails
                            self.conversation_log.append(f"Mai: {chunk.text}")
                            logger.info(f"Mai response: {chunk.text[:100]}...")

                            # Check for goodbye and auto-end session
                            if detect_goodbye(chunk.text):
                                logger.info("Goodbye detected, processing follow-up emails")

                                # Extract contact info from conversation
                                full_conversation = " ".join(self.conversation_log)
                                contact_info = extract_contact_info(full_conversation)

                                # Send follow-up emails if we have contact info
                                if contact_info:
                                    conversation_summary = "\n".join(self.conversation_log[-10:])  # Last 10 exchanges
                                    await send_follow_up_emails(contact_info, conversation_summary)

                                await asyncio.sleep(2)  # Let the goodbye message play
                                self.quit.set()
                                break

            except Exception as session_error:
                logger.error(f"Mai Live session error: {session_error}")
                logger.info("Live session failed, handler will operate in degraded mode")
            finally:
                # Clean up session
                if self.session_id and self.session_id in active_sessions:
                    del active_sessions[self.session_id]

        except Exception as e:
            logger.error(f"Error in MaiHandler start_up: {e}")
            logger.info("Handler startup failed, will operate in degraded mode")
            await asyncio.sleep(1)

    async def stream(self) -> AsyncGenerator[bytes, None]:
        """Stream audio data to Gemini"""
        while not self.quit.is_set():
            try:
                audio_data = await asyncio.wait_for(self.input_queue.get(), 0.1)
                yield audio_data
            except (asyncio.TimeoutError, TimeoutError):
                pass
            except Exception as e:
                logger.error(f"Error in audio streaming: {e}")
                break

    async def receive(self, frame: tuple[int, np.ndarray]) -> None:
        """Receive audio from client and queue for Gemini with error handling"""
        try:
            if self.quit.is_set():
                return

            _, array = frame
            array = array.squeeze()

            # Validate audio data
            if array.size == 0:
                return

            # Log user input for conversation tracking
            # Note: We can't easily convert audio to text here, but we track the interaction
            self.conversation_log.append("User: [Audio input received]")

            # Encode audio for Gemini
            audio_bytes = array.astype(np.int16).tobytes()

            # Don't queue if we're shutting down
            if not self.quit.is_set():
                try:
                    self.input_queue.put_nowait(audio_bytes)
                except asyncio.QueueFull:
                    logger.warning("Input queue full, dropping audio frame")

        except Exception as e:
            logger.error(f"Error processing incoming audio: {e}")

    async def emit(self) -> tuple[int, np.ndarray] | None:
        """Emit audio response from Gemini to client with timeout"""
        try:
            if self.quit.is_set():
                return None
            return await asyncio.wait_for(self.output_queue.get(), timeout=0.1)
        except asyncio.TimeoutError:
            return None
        except Exception as e:
            logger.error(f"Error emitting audio: {e}")
            return None

    def shutdown(self) -> None:
        """Clean shutdown of the handler"""
        logger.info("Shutting down Mai handler")
        self.quit.set()

        # Clean up session
        if self.session_id and self.session_id in active_sessions:
            del active_sessions[self.session_id]

        # Clear queues
        while not self.input_queue.empty():
            try:
                self.input_queue.get_nowait()
            except:
                break

        while not self.output_queue.empty():
            try:
                self.output_queue.get_nowait()
            except:
                break

# Enhanced Stream configuration with better error handling
if FASTRTC_AVAILABLE:
    try:
        # Audio-only stream for Mai
        audio_stream = Stream(
            modality="audio",
            mode="send-receive",
            handler=MaiHandler(),
            rtc_configuration=get_cloudflare_turn_credentials_async,
            concurrency_limit=5,
            time_limit=300,
        )

        logger.info("FastRTC Streams initialized successfully for Mai")
    except Exception as e:
        logger.error(f"Error initializing FastRTC Streams: {e}")
        # Proper fallback with required arguments
        audio_stream = Stream(
            modality="audio",
            mode="send-receive",
            handler=MaiHandler(),
            rtc_configuration=get_cloudflare_turn_credentials_async,
        )
else:
    # Fallback streams with proper arguments
    audio_stream = Stream(
        modality="audio",
        mode="send-receive",
        handler=MaiHandler(),
    )
    logger.warning("Using fallback Stream implementations")

# Request/Response models
class ChatRequest(BaseModel):
    prompt: str
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 512

class ChatResponse(BaseModel):
    response: str

class InputData(BaseModel):
    webrtc_id: str
    voice_name: str = "Aoede"  # Default to Aoede for Mai
    mode: str = "audio"

class ContactInfo(BaseModel):
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    company: Optional[str] = None
    purpose: Optional[str] = None

# Initialize FastAPI app
app = FastAPI(
    title="Mai Voice Agent - Critical Future",
    description="Mai, the intelligent voice-to-voice AI customer service assistant for Critical Future",
    version="1.0.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files for frontend
static_dir = current_dir.parent / "frontend"
if static_dir.exists():
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
    logger.info("Frontend static files mounted")
else:
    logger.warning("Frontend directory not found, skipping static file mounting")

# Mount FastRTC streams
try:
    audio_stream.mount(app, path="/audio")
    if FASTRTC_AVAILABLE:
        logger.info("FastRTC streams mounted successfully")
    else:
        logger.info("Fallback streams mounted (WebRTC features limited)")
except Exception as e:
    logger.error(f"Error mounting streams: {e}")

@app.post("/input_hook")
async def set_input_hook(body: InputData):
    """Set input parameters for WebRTC stream with validation"""
    try:
        if not FASTRTC_AVAILABLE:
            return {
                "status": "error",
                "message": "WebRTC features are not available in this deployment",
                "webrtc_id": body.webrtc_id,
                "voice": body.voice_name,
                "mode": body.mode
            }

        # Validate inputs
        if not body.webrtc_id or not body.voice_name:
            raise HTTPException(status_code=400, detail="Missing webrtc_id or voice_name")

        # Always use Aoede for Mai
        body.voice_name = "Aoede"

        # Set input for audio stream
        audio_stream.set_input(body.webrtc_id, body.voice_name)

        logger.info(f"Input set for WebRTC ID: {body.webrtc_id}, Voice: {body.voice_name}, Mode: {body.mode}")
        return {"status": "ok", "webrtc_id": body.webrtc_id, "voice": body.voice_name, "mode": body.mode}

    except Exception as e:
        logger.error(f"Error setting input: {e}")
        raise HTTPException(status_code=500, detail=f"Input hook failed: {str(e)}")

@app.post("/end_session")
async def end_session(session_id: str = None):
    """Manually end a session"""
    try:
        if session_id and session_id in active_sessions:
            session_info = active_sessions[session_id]
            handler = session_info["handler"]
            handler.shutdown()
            logger.info(f"Session {session_id} ended manually")
            return {"status": "success", "message": "Session ended"}
        else:
            # End all active sessions
            for sid, session_info in list(active_sessions.items()):
                handler = session_info["handler"]
                handler.shutdown()
            logger.info("All sessions ended")
            return {"status": "success", "message": "All sessions ended"}
    except Exception as e:
        logger.error(f"Error ending session: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to end session: {str(e)}")

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Text-based chat endpoint using Gemini for Mai"""
    try:
        # Check for goodbye in text chat
        if detect_goodbye(request.prompt):
            return ChatResponse(response="Thank you for reaching out. I've made a note of everything, and one of our team will be in touch shortly by phone or email. Have a fantastic day!")

        # Initialize Gemini client for text
        if settings.use_vertex_ai and settings.google_cloud_project:
            client = genai.Client(
                vertexai=True,
                project=settings.google_cloud_project,
                location='us-central1'
            )
        else:
            client = genai.Client(
                api_key=settings.gemini_api_key,
            )

        # Generate response
        response = await client.aio.models.generate_content(
            model="gemini-2.0-flash-exp",
            contents=[
                {"parts": [{"text": f"System: {SYSTEM_PROMPT}\n\nUser: {request.prompt}"}]}
            ]
        )

        ai_response = response.text if hasattr(response, 'text') else str(response)

        logger.info(f"Text chat response generated: {len(ai_response)} characters")
        return ChatResponse(response=ai_response)

    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")

        # Fallback response for customer service context
        fallback_response = (
            "I'm experiencing a technical difficulty right now. "
            "Please try again, or feel free to contact us <NAME_EMAIL>. "
            "We'll make sure someone from our team gets back to you promptly."
        )
        return ChatResponse(response=fallback_response)

@app.websocket("/ws")
async def websocket_text_chat(websocket: WebSocket):
    """WebSocket endpoint for text-based chat with streaming responses"""
    await websocket.accept()
    logger.info("WebSocket connection established for text chat")

    try:
        while True:
            # Receive message from client
            user_msg = await websocket.receive_text()
            logger.info(f"Received WebSocket message: {user_msg[:100]}...")

            # Check for goodbye
            if detect_goodbye(user_msg):
                goodbye_response = (
                    "Thank you for reaching out. I've made a note of everything, and one of our team "
                    "will be in touch shortly by phone or email. Have a fantastic day!"
                )
                await websocket.send_text(goodbye_response)
                continue

            try:
                # Initialize Gemini client
                if settings.use_vertex_ai and settings.google_cloud_project:
                    client = genai.Client(
                        vertexai=True,
                        project=settings.google_cloud_project,
                        location='us-central1'
                    )
                else:
                    client = genai.Client(
                        api_key=settings.gemini_api_key,
                    )

                # Generate response
                response = await client.aio.models.generate_content(
                    model="gemini-2.0-flash-exp",
                    contents=[
                        {"parts": [{"text": f"System: {SYSTEM_PROMPT}\n\nUser: {user_msg}"}]}
                    ]
                )

                # Send the complete response at once
                if hasattr(response, 'text') and response.text:
                    # Clean up the response text
                    clean_text = response.text

                    # Remove common voice artifacts
                    artifacts_to_remove = [
                        "[pause]", "...", "… [pause] …", "[PAUSE]",
                        "*pause*", "(pause)", "... [pause] ...",
                        "[breath]", "*breath*", "(breath)"
                    ]

                    for artifact in artifacts_to_remove:
                        clean_text = clean_text.replace(artifact, "")

                    # Clean up extra spaces and line breaks
                    clean_text = " ".join(clean_text.split())

                    await websocket.send_text(clean_text)

                    # Extract contact info and send follow-up if conversation is ending
                    if detect_goodbye(clean_text):
                        contact_info = extract_contact_info(user_msg + " " + clean_text)
                        if contact_info:
                            conversation_summary = f"WebSocket conversation:\nUser: {user_msg}\nMai: {clean_text}"
                            await send_follow_up_emails(contact_info, conversation_summary)
                else:
                    await websocket.send_text("I apologize, I couldn't generate a proper response. How else can I help you?")

            except Exception as e:
                logger.error(f"Error generating response: {e}")
                await websocket.send_text(
                    "I apologize, but I'm having technical difficulties. "
                    "Please try again or contact us <NAME_EMAIL>."
                )

    except WebSocketDisconnect:
        logger.info("WebSocket client disconnected")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        try:
            await websocket.send_text(f"Connection error occurred: {str(e)}")
        except:
            pass

@app.post("/submit_contact")
async def submit_contact_info(contact: ContactInfo):
    """Submit contact information and trigger follow-up emails"""
    try:
        # Convert contact info to dict
        contact_dict = contact.dict(exclude_none=True)

        # Create a simple conversation summary
        conversation_summary = f"Contact form submission with the following details: {contact_dict}"

        # Send follow-up emails
        success = await send_follow_up_emails(contact_dict, conversation_summary)

        if success:
            return {"status": "success", "message": "Thank you! We've received your information and will be in touch soon."}
        else:
            return {"status": "error", "message": "There was an issue processing your request. Please try again."}

    except Exception as e:
        logger.error(f"Error submitting contact info: {e}")
        raise HTTPException(status_code=500, detail="Failed to process contact information")

@app.get("/api/voices")
async def get_available_voices():
    """Get list of available Gemini voices (Mai always uses Aoede)"""
    return {
        "voices": [
            {"id": "Aoede", "name": "Aoede", "description": "Melodic, soothing voice (Mai's voice)"},
        ],
        "fastrtc_available": FASTRTC_AVAILABLE,
        "default_voice": "Aoede"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint for Railway deployment"""
    try:
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "service": "mai-voice-agent",
            "version": "1.0.0",
            "fastrtc_available": FASTRTC_AVAILABLE,
            "voice_support": FASTRTC_AVAILABLE,
            "active_sessions": len(active_sessions),
            "default_voice": "Aoede"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")

@app.get("/debug")
async def debug_info():
    """Debug endpoint to check file existence and configuration"""
    frontend_dir = current_dir.parent / "frontend"

    return {
        "current_directory": str(current_dir),
        "frontend_dir_exists": frontend_dir.exists(),
        "gemini_api_key_set": bool(settings.gemini_api_key),
        "email_configured": bool(settings.email_address and settings.email_password),
        "fastrtc_available": FASTRTC_AVAILABLE,
        "fastrtc_error": FASTRTC_ERROR,
        "voice_support": FASTRTC_AVAILABLE,
        "active_sessions": list(active_sessions.keys()),
        "files_in_directory": [f.name for f in current_dir.iterdir() if f.is_file()][:10],
        "python_version": sys.version,
        "default_voice": "Aoede",
        "recommendations": [
            "Text chat is always available",
            "Voice chat requires FastRTC compatibility",
            f"Current issue: {FASTRTC_ERROR}" if FASTRTC_ERROR else "No issues detected"
        ]
    }

@app.get("/", response_class=HTMLResponse)
async def get_main_page():
    """Serve the main HTML interface for Mai Voice Agent"""
    try:
        # Check if index.html exists in frontend directory
        frontend_dir = current_dir.parent / "frontend"
        html_file = frontend_dir / "index.html"

        if html_file.exists():
            try:
                html_content = html_file.read_text(encoding='utf-8')
                return HTMLResponse(content=html_content)
            except Exception as read_error:
                logger.error(f"Error reading index.html: {read_error}")
                return HTMLResponse(content=f"""
<!DOCTYPE html>
<html>
<head><title>Mai Voice Agent - File Error</title></head>
<body>
    <h1>File Read Error</h1>
    <p>Could not read index.html: {read_error}</p>
    <p><a href="/debug">Debug Info</a></p>
</body>
</html>
                """, status_code=503)
        else:
            # Return a basic HTML page if frontend not found
            return HTMLResponse(content="""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mai Voice Agent - Critical Future</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            max-width: 600px;
            padding: 40px;
            background: rgba(30, 41, 59, 0.95);
            border-radius: 20px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        h1 {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #6366f1, #8b5cf6, #06b6d4);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            margin-bottom: 1rem;
        }
        .subtitle {
            color: #94a3b8;
            font-size: 1.2rem;
            margin-bottom: 2rem;
        }
        .status {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .links {
            margin-top: 30px;
        }
        .links a {
            color: #6366f1;
            text-decoration: none;
            margin: 0 15px;
            padding: 10px 20px;
            border: 1px solid #6366f1;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .links a:hover {
            background: #6366f1;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Mai Voice Agent</h1>
        <p class="subtitle">AI Customer Service Assistant for Critical Future</p>

        <div class="status">
            <h3>🤖 Mai is Ready</h3>
            <p>Your intelligent voice assistant is online and ready to help with customer inquiries.</p>
        </div>

        <div class="links">
            <a href="/health">Health Check</a>
            <a href="/debug">Debug Info</a>
            <a href="/api/voices">Voice Settings</a>
        </div>

        <p style="margin-top: 30px; color: #64748b; font-size: 0.9rem;">
            Frontend interface not found. Please ensure the frontend files are properly deployed.
        </p>
    </div>
</body>
</html>
            """)

    except Exception as e:
        logger.error(f"Error serving main page: {e}")
        return HTMLResponse(content=f"""
<!DOCTYPE html>
<html>
<head><title>Mai Voice Agent - Error</title></head>
<body style="font-family: Arial, sans-serif; background: #0f172a; color: white; padding: 20px;">
    <h1>Mai Voice Agent - Error</h1>
    <p>An error occurred while loading the interface: {str(e)}</p>
    <p><a href="/debug" style="color: #6366f1;">Debug Information</a></p>
</body>
</html>
        """, status_code=500)

if __name__ == "__main__":
    import uvicorn

    # Get port from environment or use default
    port = int(os.getenv("PORT", 8000))

    logger.info(f"Starting Mai Voice Agent on port {port}")
    logger.info(f"FastRTC Available: {FASTRTC_AVAILABLE}")
    logger.info(f"Email configured: {bool(settings.email_address and settings.email_password)}")

    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        log_level="info"
    )
