<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mai - AI Voice Assistant | Critical Future</title>
    <style>
        :root {
            --color-accent: #6366f1;
            --color-background: #0f172a;
            --color-surface: #1e293b;
            --color-text: #e2e8f0;
            --color-success: #10b981;
            --color-warning: #f59e0b;
            --color-error: #ef4444;
            --boxSize: 8px;
            --gutter: 4px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, var(--color-background) 0%, #1e1b4b 100%);
            color: var(--color-text);
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 1rem;
        }

        .header h1 {
            font-size: 3rem;
            background: linear-gradient(135deg, #6366f1, #8b5cf6, #06b6d4);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            margin-bottom: 0.5rem;
        }

        .header .subtitle {
            color: #94a3b8;
            font-size: 1.2rem;
            margin-bottom: 1rem;
        }

        .mai-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 1rem;
            border: 3px solid var(--color-accent);
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
        }

        .mai-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .disclaimer {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid rgba(245, 158, 11, 0.3);
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            line-height: 1.5;
            max-width: 600px;
        }

        .container {
            width: 90%;
            max-width: 1200px;
            background: rgba(30, 41, 59, 0.95);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .mode-selector {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
            background: rgba(15, 23, 42, 0.5);
            border-radius: 0.75rem;
            padding: 0.5rem;
        }

        .mode-btn {
            flex: 1;
            padding: 0.75rem 1.5rem;
            border: none;
            background: transparent;
            color: var(--color-text);
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            font-weight: 500;
        }

        .mode-btn.active {
            background: var(--color-accent);
            color: white;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
        }

        .mode-btn:hover:not(.active) {
            background: rgba(99, 102, 241, 0.1);
        }

        .chat-interface {
            display: none;
            flex-direction: column;
            height: 500px;
        }

        .chat-interface.active {
            display: flex;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            background: rgba(15, 23, 42, 0.3);
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .message {
            margin-bottom: 1rem;
            padding: 0.75rem 1rem;
            border-radius: 0.75rem;
            max-width: 80%;
            word-wrap: break-word;
        }

        .message.user {
            background: var(--color-accent);
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .message.mai {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: var(--color-text);
        }

        .chat-input-container {
            display: flex;
            gap: 0.5rem;
        }

        .chat-input {
            flex: 1;
            padding: 0.75rem 1rem;
            background: rgba(15, 23, 42, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 0.5rem;
            color: var(--color-text);
            font-size: 1rem;
        }

        .chat-input:focus {
            outline: none;
            border-color: var(--color-accent);
            box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
        }

        .send-btn {
            padding: 0.75rem 1.5rem;
            background: var(--color-accent);
            color: white;
            border: none;
            border-radius: 0.5rem;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            background: #5855eb;
            transform: translateY(-1px);
        }

        .voice-interface {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .voice-interface.active {
            display: block;
        }

        .voice-controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin: 2rem 0;
        }

        .voice-btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 0.75rem;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .voice-btn.start {
            background: var(--color-success);
            color: white;
        }

        .voice-btn.stop {
            background: var(--color-error);
            color: white;
        }

        .voice-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .voice-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-size: 0.9rem;
            font-weight: 500;
            margin: 1rem 0;
        }

        .status-indicator.connected {
            background: rgba(16, 185, 129, 0.1);
            color: var(--color-success);
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .status-indicator.disconnected {
            background: rgba(239, 68, 68, 0.1);
            color: var(--color-error);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .status-indicator.connecting {
            background: rgba(245, 158, 11, 0.1);
            color: var(--color-warning);
            border: 1px solid rgba(245, 158, 11, 0.3);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .contact-form {
            display: none;
            background: rgba(15, 23, 42, 0.3);
            padding: 2rem;
            border-radius: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            margin-top: 2rem;
        }

        .contact-form.active {
            display: block;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--color-text);
            font-weight: 500;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            background: rgba(15, 23, 42, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 0.5rem;
            color: var(--color-text);
            font-size: 1rem;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--color-accent);
            box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
        }

        .submit-btn {
            width: 100%;
            padding: 1rem;
            background: var(--color-accent);
            color: white;
            border: none;
            border-radius: 0.5rem;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .submit-btn:hover {
            background: #5855eb;
            transform: translateY(-1px);
        }

        .footer {
            text-align: center;
            margin-top: 2rem;
            padding: 1rem;
            color: #64748b;
            font-size: 0.9rem;
        }

        .footer a {
            color: var(--color-accent);
            text-decoration: none;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .container {
                width: 95%;
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .mode-selector {
                flex-direction: column;
            }
            
            .voice-controls {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>

<body>
    <div class="header">
        <div class="mai-avatar">
            <img src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" alt="Mai Avatar" id="maiAvatar">
        </div>
        <h1>Mai</h1>
        <p class="subtitle">AI Voice Assistant for Critical Future</p>
        
        <div class="disclaimer">
            <strong>🤖 Meet Mai:</strong> Your intelligent AI assistant for Critical Future. 
            Mai can help with inquiries, capture contact details, and ensure our team follows up with you promptly. 
            Choose your preferred communication method below.
        </div>
    </div>

    <div class="container">
        <div class="mode-selector">
            <button class="mode-btn active" data-mode="chat">💬 Text Chat</button>
            <button class="mode-btn" data-mode="voice">🎤 Voice Chat</button>
            <button class="mode-btn" data-mode="contact">📝 Contact Form</button>
        </div>

        <!-- Text Chat Interface -->
        <div class="chat-interface active" id="chatInterface">
            <div class="chat-messages" id="chatMessages">
                <div class="message mai">
                    Hi there! You're speaking with Mai, the AI assistant for Critical Future. 
                    I'm here to help, take notes, and ensure the right person from our team follows up with you directly. 
                    How can I assist you today?
                </div>
            </div>
            <div class="chat-input-container">
                <input type="text" class="chat-input" id="chatInput" placeholder="Type your message here..." maxlength="500">
                <button class="send-btn" id="sendBtn">Send</button>
            </div>
        </div>

        <!-- Voice Chat Interface -->
        <div class="voice-interface" id="voiceInterface">
            <div class="status-indicator disconnected" id="voiceStatus">
                <span class="status-dot"></span>
                <span>Voice chat ready</span>
            </div>
            
            <p style="margin: 1rem 0; color: #94a3b8;">
                Click "Start Voice Chat" to begin speaking with Mai using your microphone.
            </p>
            
            <div class="voice-controls">
                <button class="voice-btn start" id="startVoiceBtn">🎤 Start Voice Chat</button>
                <button class="voice-btn stop" id="stopVoiceBtn" disabled>🛑 Stop Voice Chat</button>
            </div>
            
            <div id="voiceMessages" style="margin-top: 2rem; text-align: left;">
                <!-- Voice conversation will appear here -->
            </div>
        </div>

        <!-- Contact Form -->
        <div class="contact-form" id="contactForm">
            <h3 style="margin-bottom: 1.5rem; color: var(--color-text);">Get in Touch with Critical Future</h3>
            
            <form id="contactFormElement">
                <div class="form-group">
                    <label for="contactName">Full Name *</label>
                    <input type="text" id="contactName" name="name" required>
                </div>
                
                <div class="form-group">
                    <label for="contactEmail">Email Address *</label>
                    <input type="email" id="contactEmail" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="contactPhone">Phone Number</label>
                    <input type="tel" id="contactPhone" name="phone">
                </div>
                
                <div class="form-group">
                    <label for="contactCompany">Company</label>
                    <input type="text" id="contactCompany" name="company">
                </div>
                
                <div class="form-group">
                    <label for="contactPurpose">How can we help you? *</label>
                    <textarea id="contactPurpose" name="purpose" rows="4" required placeholder="Tell us about your AI or strategy needs..."></textarea>
                </div>
                
                <button type="submit" class="submit-btn">Submit Inquiry</button>
            </form>
        </div>
    </div>

    <div class="footer">
        <p>Powered by <a href="https://criticalfuture.co.uk" target="_blank" rel="noopener">Cratical Future</a> | 
        AI & Strategy Development Agency</p>
    </div>

    <script src="script.js"></script>
</body>

</html>
