<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mai - AI Voice Assistant | Critical Future</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- External CSS -->
    <link rel="stylesheet" href="styles.css">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="assets/cf_logo.png">
</head>

<body>
    <div class="app-container">
        <div class="header">
            <div class="mai-avatar">
                <img src="assets/mai.gif" alt="Mai Avatar" id="maiAvatar">
            </div>
            <h1>Mai</h1>
            <p class="subtitle">AI Voice Assistant for Critical Future</p>

            <div class="disclaimer">
                <strong>🤖 Meet Mai:</strong> Your intelligent AI assistant for Critical Future.
                Mai can help with inquiries, capture contact details, and ensure our team follows up with you promptly.
                Choose your preferred communication method below.
            </div>
        </div>

        <div class="container">
            <div class="mode-selector">
                <button type="button" class="mode-btn active" data-mode="chat">💬 Text Chat</button>
                <button type="button" class="mode-btn" data-mode="voice">🎤 Voice Chat</button>
                <button type="button" class="mode-btn" data-mode="contact">📝 Contact Form</button>
            </div>

            <!-- Text Chat Interface -->
            <div class="chat-interface active" id="chatInterface">
                <div class="chat-messages" id="chatMessages">
                    <div class="message mai">
                        Hi there! You're speaking with Mai, the AI assistant for Critical Future.
                        I'm here to help, take notes, and ensure the right person from our team follows up with you directly.
                        How can I assist you today?
                    </div>
                </div>
                <div class="chat-input-container">
                    <textarea class="chat-input" id="chatInput" placeholder="Type your message here..." maxlength="500" rows="2"></textarea>
                    <button type="button" class="send-btn" id="sendBtn">Send</button>
                </div>
            </div>

            <!-- Voice Chat Interface -->
            <div class="voice-interface" id="voiceInterface">
                <div class="status-indicator disconnected" id="voiceStatus">
                    <span class="status-dot"></span>
                    <span>Voice chat ready</span>
                </div>

                <p class="voice-description">
                    Click "Start Voice Chat" to begin speaking with Mai using your microphone.
                </p>

                <div class="voice-controls">
                    <button type="button" class="voice-btn start" id="startVoiceBtn">🎤 Start Voice Chat</button>
                    <button type="button" class="voice-btn stop" id="stopVoiceBtn" disabled>🛑 Stop Voice Chat</button>
                </div>

                <div id="voiceMessages" class="voice-messages">
                    <!-- Voice conversation will appear here -->
                </div>
            </div>

            <!-- Video Chat Interface -->
            <div class="video-interface" id="videoInterface">
                <div class="status-indicator disconnected" id="videoStatus">
                    <span class="status-dot"></span>
                    <span>Video chat ready</span>
                </div>

                <p class="video-description">
                    Start a video call with Mai. She can see you and provide more personalized assistance.
                </p>

                <div class="video-container">
                    <div class="video-element local">
                        <video id="localVideo" autoplay muted playsinline></video>
                        <div class="video-label">You</div>
                    </div>
                    <div class="video-element remote">
                        <video id="remoteVideo" autoplay playsinline></video>
                        <div class="video-label">Mai</div>
                    </div>
                </div>

                <div class="voice-controls">
                    <button type="button" class="voice-btn start" id="startVideoBtn">📹 Start Video Chat</button>
                    <button type="button" class="voice-btn stop" id="stopVideoBtn" disabled>🛑 Stop Video Chat</button>
                </div>

                <div id="videoMessages" class="video-messages">
                    <!-- Video conversation will appear here -->
                </div>
            </div>

            <!-- Contact Form -->
            <div class="contact-form" id="contactForm">
                <h3>Get in Touch with Critical Future</h3>

                <form id="contactFormElement">
                    <div class="form-group">
                        <label for="contactName">Full Name *</label>
                        <input type="text" id="contactName" name="name" required>
                    </div>

                    <div class="form-group">
                        <label for="contactEmail">Email Address *</label>
                        <input type="email" id="contactEmail" name="email" required>
                    </div>

                    <div class="form-group">
                        <label for="contactPhone">Phone Number</label>
                        <input type="tel" id="contactPhone" name="phone">
                    </div>

                    <div class="form-group">
                        <label for="contactCompany">Company</label>
                        <input type="text" id="contactCompany" name="company">
                    </div>

                    <div class="form-group">
                        <label for="contactPurpose">How can we help you? *</label>
                        <textarea id="contactPurpose" name="purpose" rows="4" required placeholder="Tell us about your AI or strategy needs..."></textarea>
                    </div>

                    <button type="submit" class="submit-btn">Submit Inquiry</button>
                </form>
            </div>
        </div>

        <div class="footer">
            <p>Powered by <a href="https://criticalfutureglobal.com" target="_blank" rel="noopener">Critical Future</a> |
            AI & Strategy Development Agency</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>

</html>