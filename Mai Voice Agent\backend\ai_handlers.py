"""
AI handlers for Mai Voice Agent
Manages Gemini AI interactions and voice processing
"""

import logging
import asyncio
import uuid
import json
import base64
from typing import Optional, Dict, Any, List
from datetime import datetime

import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold

# Try to import Gemini Live API components
try:
    from google import genai as genai_live
    GEMINI_LIVE_AVAILABLE = True
except ImportError as e:
    GEMINI_LIVE_AVAILABLE = False
    print(f"❌ Gemini Live API not available: {e}")

from config import settings, SYSTEM_PROMPT
from models import (
    ChatRequest, ChatResponse, VoiceRequest, VideoRequest,
    ConversationMemory, MessageType, SessionType, session_manager
)

logger = logging.getLogger(__name__)

class AIHandler:
    """Handles AI interactions with Gemini"""
    
    def __init__(self):
        self.setup_gemini()
        self.active_voice_sessions: Dict[str, Any] = {}
        self.active_video_sessions: Dict[str, Any] = {}
    
    def setup_gemini(self):
        """Initialize Gemini AI configuration"""
        try:
            genai.configure(api_key=settings.gemini_api_key)
            
            # Configure safety settings
            self.safety_settings = {
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            }
            
            # Initialize model
            self.model = genai.GenerativeModel(
                model_name="gemini-1.5-flash",
                safety_settings=self.safety_settings,
                system_instruction=SYSTEM_PROMPT
            )
            
            logger.info("✅ Gemini AI initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Gemini AI: {e}")
            raise
    
    def extract_contact_info_from_text(self, text: str) -> Dict[str, Optional[str]]:
        """Extract contact information from conversation text using AI"""
        try:
            extraction_prompt = f"""
            Extract contact information from this conversation text. Return ONLY a JSON object with these fields:
            - name: person's full name (or null)
            - email: email address (or null)
            - phone: phone number (or null)
            - company: company name (or null)
            - purpose: reason for contacting/inquiry purpose (or null)
            
            Conversation text:
            {text}
            
            JSON:
            """
            
            response = self.model.generate_content(extraction_prompt)
            
            # Try to parse JSON from response
            import json
            try:
                contact_data = json.loads(response.text.strip())
                return contact_data
            except json.JSONDecodeError:
                logger.warning("Failed to parse contact extraction JSON")
                return {}
                
        except Exception as e:
            logger.error(f"Error extracting contact info: {e}")
            return {}
    
    async def generate_chat_response(self, request: ChatRequest) -> ChatResponse:
        """Generate AI response for text chat"""
        try:
            # Get or create session
            session_id = request.session_id or str(uuid.uuid4())
            memory = session_manager.get_session(session_id)
            
            if not memory:
                memory = session_manager.create_session(session_id, SessionType.TEXT)
            
            # Add user message to memory
            memory.add_message(MessageType.USER, request.prompt)
            
            # Build conversation context
            conversation_history = []
            for msg in memory.messages[-10:]:  # Last 10 messages for context
                role = "user" if msg.type == MessageType.USER else "model"
                conversation_history.append({
                    "role": role,
                    "parts": [msg.content]
                })
            
            # Generate response
            chat = self.model.start_chat(history=conversation_history[:-1])  # Exclude current message
            response = chat.send_message(
                request.prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=request.temperature,
                    max_output_tokens=request.max_tokens,
                )
            )
            
            ai_response = response.text.strip()
            
            # Add AI response to memory
            memory.add_message(MessageType.MAI, ai_response)
            
            # Extract contact info if conversation seems to contain it
            if any(keyword in request.prompt.lower() for keyword in ['email', 'phone', 'company', 'name is', "i'm"]):
                contact_data = self.extract_contact_info_from_text(memory.get_conversation_text())
                if contact_data:
                    # Update contact info in memory
                    for key, value in contact_data.items():
                        if value and hasattr(memory.contact_info, key):
                            setattr(memory.contact_info, key, value)
            
            logger.info(f"Generated chat response for session {session_id}")
            
            return ChatResponse(
                response=ai_response,
                session_id=session_id,
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"Error generating chat response: {e}")
            return ChatResponse(
                response="I apologize, but I'm experiencing technical difficulties. Please try again or contact our team directly.",
                session_id=request.session_id,
                timestamp=datetime.utcnow()
            )
    
    async def start_voice_session(self, request: VoiceRequest) -> Dict[str, Any]:
        """Start a voice chat session"""
        try:
            session_id = request.session_id or str(uuid.uuid4())
            
            # Create or get session memory
            memory = session_manager.get_session(session_id)
            if not memory:
                memory = session_manager.create_session(session_id, SessionType.VOICE)
            
            # Store voice session info
            voice_session = {
                "session_id": session_id,
                "webrtc_id": request.webrtc_id,
                "voice_name": request.voice_name,
                "started_at": datetime.utcnow(),
                "status": "connected"
            }
            
            self.active_voice_sessions[session_id] = voice_session
            
            logger.info(f"Started voice session {session_id} with voice {request.voice_name}")
            
            return {
                "status": "connected",
                "session_id": session_id,
                "voice_name": request.voice_name,
                "message": "Voice chat session started successfully"
            }
            
        except Exception as e:
            logger.error(f"Error starting voice session: {e}")
            return {
                "status": "error",
                "message": f"Failed to start voice session: {str(e)}"
            }
    
    async def start_video_session(self, request: VideoRequest) -> Dict[str, Any]:
        """Start a video chat session"""
        try:
            session_id = request.session_id or str(uuid.uuid4())
            
            # Create or get session memory
            memory = session_manager.get_session(session_id)
            if not memory:
                memory = session_manager.create_session(session_id, SessionType.VIDEO)
            
            # Store video session info
            video_session = {
                "session_id": session_id,
                "webrtc_id": request.webrtc_id,
                "voice_name": request.voice_name,
                "enable_video": request.enable_video,
                "started_at": datetime.utcnow(),
                "status": "connected"
            }
            
            self.active_video_sessions[session_id] = video_session
            
            logger.info(f"Started video session {session_id} with voice {request.voice_name}")
            
            return {
                "status": "connected",
                "session_id": session_id,
                "voice_name": request.voice_name,
                "video_enabled": request.enable_video,
                "message": "Video chat session started successfully"
            }
            
        except Exception as e:
            logger.error(f"Error starting video session: {e}")
            return {
                "status": "error",
                "message": f"Failed to start video session: {str(e)}"
            }
    
    async def end_session(self, session_id: str) -> Dict[str, Any]:
        """End a session and trigger follow-up emails if contact info is available"""
        try:
            memory = session_manager.get_session(session_id)
            
            if not memory:
                return {"status": "error", "message": "Session not found"}
            
            # Check if we have contact info for follow-up
            contact_info = memory.contact_info
            should_send_emails = any([
                contact_info.name,
                contact_info.email,
                contact_info.phone,
                contact_info.company
            ])
            
            emails_sent = 0
            if should_send_emails:
                try:
                    from email_service import email_service
                    email_response = await email_service.send_follow_up_emails(contact_info, memory)
                    emails_sent = email_response.emails_sent
                    logger.info(f"Follow-up emails sent: {emails_sent}")
                except Exception as e:
                    logger.error(f"Failed to send follow-up emails: {e}")
            
            # Clean up session
            session_manager.end_session(session_id)
            
            # Remove from active sessions
            self.active_voice_sessions.pop(session_id, None)
            self.active_video_sessions.pop(session_id, None)
            
            logger.info(f"Ended session {session_id}, emails sent: {emails_sent}")
            
            return {
                "status": "success",
                "message": f"Session ended successfully. {emails_sent} follow-up emails sent.",
                "emails_sent": emails_sent
            }
            
        except Exception as e:
            logger.error(f"Error ending session {session_id}: {e}")
            return {
                "status": "error",
                "message": f"Failed to end session: {str(e)}"
            }
    
    def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """Get status of a session"""
        memory = session_manager.get_session(session_id)
        
        if not memory:
            return {"status": "not_found", "message": "Session not found"}
        
        voice_session = self.active_voice_sessions.get(session_id)
        video_session = self.active_video_sessions.get(session_id)
        
        return {
            "status": "active",
            "session_id": session_id,
            "session_type": memory.session_type.value,
            "started_at": memory.started_at.isoformat(),
            "last_activity": memory.last_activity.isoformat(),
            "message_count": len(memory.messages),
            "has_contact_info": any([
                memory.contact_info.name,
                memory.contact_info.email,
                memory.contact_info.phone
            ]),
            "voice_active": voice_session is not None,
            "video_active": video_session is not None
        }

    async def process_voice_input(self, session_id: str, audio_data: str) -> Dict[str, Any]:
        """Process voice input and generate voice response with Gemini Live API"""
        try:
            # Get session memory
            memory = session_manager.get_session(session_id)
            if not memory:
                return {"error": "Session not found"}

            if GEMINI_LIVE_AVAILABLE:
                try:
                    # Use Gemini Live API for real-time voice processing
                    # This would be the actual implementation with Gemini Live
                    logger.info("Processing voice input with Gemini Live API")

                    # For now, simulate the response until Gemini Live is fully integrated
                    # In production, this would use the actual Gemini Live WebSocket connection
                    user_message = "I'm speaking to Mai through voice chat"

                    # Generate AI response using regular Gemini
                    chat_request = ChatRequest(prompt=user_message, session_id=session_id)
                    response = await self.generate_chat_response(chat_request)

                    # Simulate Aoede voice response
                    # In production, this would be actual audio from Gemini Live
                    audio_response = self._generate_mock_audio_response(response.response)

                    return {
                        "audio_data": audio_response,
                        "text": response.response,
                        "session_id": session_id,
                        "voice_name": "Aoede"
                    }

                except Exception as live_error:
                    logger.error(f"Gemini Live API error: {live_error}")
                    # Fallback to text-based response

            # Fallback: Process as text and generate mock audio
            user_message = "Hello Mai, I'm speaking to you via voice chat"

            # Generate text response
            chat_request = ChatRequest(prompt=user_message, session_id=session_id)
            response = await self.generate_chat_response(chat_request)

            # Generate mock audio response
            audio_response = self._generate_mock_audio_response(response.response)

            return {
                "audio_data": audio_response,
                "text": response.response,
                "session_id": session_id,
                "voice_name": "Aoede"
            }

        except Exception as e:
            logger.error(f"Voice processing error: {e}")
            return {"error": str(e)}

    def _generate_mock_audio_response(self, text: str) -> str:
        """Generate a mock audio response (base64 encoded)"""
        try:
            # This is a placeholder for actual TTS
            # In production, this would use Gemini's Aoede voice or another TTS service
            mock_audio = f"Mock audio for: {text[:50]}..."
            return base64.b64encode(mock_audio.encode()).decode()
        except Exception as e:
            logger.error(f"Error generating mock audio: {e}")
            return base64.b64encode(b"Mock audio response").decode()

    async def process_video_input(self, session_id: str, audio_data: str, video_data: str) -> Dict[str, Any]:
        """Process video and audio input and generate response"""
        try:
            # Get session memory
            memory = session_manager.get_session(session_id)
            if not memory:
                return {"error": "Session not found"}

            # For now, simulate video processing
            # In a real implementation, you would:
            # 1. Process video frames for visual context
            # 2. Convert audio_data to text
            # 3. Generate AI response considering visual context
            # 4. Convert response to speech

            # Simulate processing
            user_message = "Hello Mai, you can see me now"  # This would be from STT + visual analysis

            # Generate text response
            chat_request = ChatRequest(prompt=user_message, session_id=session_id)
            response = await self.generate_chat_response(chat_request)

            # Simulate text-to-speech
            audio_response = "base64_encoded_audio_response"

            return {
                "audio_data": audio_response,
                "text": response.response,
                "session_id": session_id
            }

        except Exception as e:
            logger.error(f"Video processing error: {e}")
            return {"error": str(e)}

    async def end_voice_session(self, session_id: str):
        """End voice session and clean up"""
        try:
            if session_id in self.active_voice_sessions:
                del self.active_voice_sessions[session_id]
                logger.info(f"Ended voice session: {session_id}")

                # End the main session and send emails
                await self.end_session(session_id)

        except Exception as e:
            logger.error(f"Error ending voice session: {e}")

    async def end_video_session(self, session_id: str):
        """End video session and clean up"""
        try:
            if session_id in self.active_video_sessions:
                del self.active_video_sessions[session_id]
                logger.info(f"Ended video session: {session_id}")

                # End the main session and send emails
                await self.end_session(session_id)

        except Exception as e:
            logger.error(f"Error ending video session: {e}")

    def cleanup_inactive_sessions(self):
        """Clean up old inactive sessions"""
        try:
            cleaned = session_manager.cleanup_old_sessions(max_age_minutes=30)
            if cleaned > 0:
                logger.info(f"Cleaned up {cleaned} inactive sessions")
        except Exception as e:
            logger.error(f"Error cleaning up sessions: {e}")

# Global AI handler instance
ai_handler = AIHandler()
