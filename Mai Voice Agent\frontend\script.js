/**
 * Mai Voice Agent Frontend JavaScript
 * Enhanced with video chat, voice chat, and conversation memory
 */

// Global state management
const AppState = {
    currentMode: 'chat',
    sessionId: null,
    isConnected: false,
    conversationMemory: [],
    contactInfo: {},

    // WebRTC state
    localStream: null,
    remoteStream: null,
    peerConnection: null,
    websocket: null,

    // Voice state
    isRecording: false,
    mediaRecorder: null,
    audioChunks: [],

    // Video state
    isVideoActive: false,
    localVideo: null,
    remoteVideo: null
};

// Configuration
const CONFIG = {
    websocketUrl: `ws://${window.location.host}/api/ws`,
    apiBaseUrl: `/api`,
    iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' }
    ]
};

// Utility functions
const Utils = {
    generateSessionId() {
        return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    },

    formatTimestamp(date = new Date()) {
        return date.toLocaleTimeString();
    },

    sanitizeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;

        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '15px 20px',
            borderRadius: '8px',
            color: 'white',
            fontWeight: '600',
            zIndex: '10000',
            maxWidth: '300px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });

        // Set background color based on type
        const colors = {
            info: '#6366f1',
            success: '#10b981',
            warning: '#f59e0b',
            error: '#ef4444'
        };
        notification.style.backgroundColor = colors[type] || colors.info;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);
    },

    async apiCall(endpoint, options = {}) {
        try {
            const response = await fetch(`${CONFIG.apiBaseUrl}${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });

            if (!response.ok) {
                throw new Error(`API call failed: ${response.status} ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API call error:', error);
            Utils.showNotification(`API Error: ${error.message}`, 'error');
            throw error;
        }
    }
};

// Mode switching functionality
const ModeManager = {
    init() {
        const modeButtons = document.querySelectorAll('.mode-btn');
        modeButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const mode = e.target.dataset.mode;
                this.switchMode(mode);
            });
        });
    },

    switchMode(mode) {
        if (AppState.currentMode === mode) return;

        // Clean up current mode
        this.cleanupCurrentMode();

        // Update UI
        document.querySelectorAll('.mode-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-mode="${mode}"]`).classList.add('active');

        // Hide all interfaces
        document.querySelectorAll('.chat-interface, .voice-interface, .video-interface, .contact-form').forEach(el => {
            el.classList.remove('active');
        });

        // Show selected interface
        const interfaceMap = {
            'chat': 'chatInterface',
            'voice': 'voiceInterface',
            'video': 'videoInterface',
            'contact': 'contactForm'
        };

        const targetInterface = document.getElementById(interfaceMap[mode]);
        if (targetInterface) {
            targetInterface.classList.add('active');
        }

        AppState.currentMode = mode;

        // Initialize mode-specific functionality
        this.initializeMode(mode);

        Utils.showNotification(`Switched to ${mode} mode`, 'info');
    },

    cleanupCurrentMode() {
        switch (AppState.currentMode) {
            case 'voice':
                VoiceChat.stop();
                break;
            case 'video':
                VideoChat.stop();
                break;
            case 'chat':
                TextChat.disconnect();
                break;
        }
    },

    initializeMode(mode) {
        switch (mode) {
            case 'chat':
                TextChat.init();
                break;
            case 'voice':
                VoiceChat.init();
                break;
            case 'video':
                VideoChat.init();
                break;
            case 'contact':
                ContactForm.init();
                break;
        }
    }
};

// Text Chat functionality
const TextChat = {
    websocket: null,

    init() {
        this.setupEventListeners();
        this.connect();
    },

    setupEventListeners() {
        const sendBtn = document.getElementById('sendBtn');
        const chatInput = document.getElementById('chatInput');

        if (sendBtn) {
            sendBtn.addEventListener('click', () => this.sendMessage());
        }

        if (chatInput) {
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
        }
    },

    connect() {
        if (!AppState.sessionId) {
            AppState.sessionId = Utils.generateSessionId();
        }

        try {
            this.websocket = new WebSocket(`${CONFIG.websocketUrl}/${AppState.sessionId}`);

            this.websocket.onopen = () => {
                AppState.isConnected = true;
                Utils.showNotification('Connected to Mai', 'success');
                this.updateConnectionStatus('connected');
            };

            this.websocket.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleMessage(data);
                } catch (error) {
                    console.error('Error parsing WebSocket message:', error);
                    this.addMessage('mai', 'Sorry, I had trouble understanding that response.');
                }
            };

            this.websocket.onclose = () => {
                AppState.isConnected = false;
                this.updateConnectionStatus('disconnected');
                Utils.showNotification('Disconnected from Mai', 'warning');
            };

            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                Utils.showNotification('Connection error occurred', 'error');
            };

        } catch (error) {
            console.error('Failed to connect WebSocket:', error);
            Utils.showNotification('Failed to connect to chat service', 'error');
        }
    },

    disconnect() {
        if (this.websocket) {
            this.websocket.close();
            this.websocket = null;
        }
    },

    sendMessage() {
        const chatInput = document.getElementById('chatInput');
        const message = chatInput.value.trim();

        if (!message) return;

        if (!AppState.isConnected) {
            Utils.showNotification('Not connected to chat service', 'error');
            return;
        }

        // Add user message to UI
        this.addMessage('user', message);

        // Store in conversation memory
        AppState.conversationMemory.push({
            type: 'user',
            content: message,
            timestamp: new Date()
        });

        // Send to backend
        this.websocket.send(JSON.stringify({
            type: 'chat',
            message: message,
            session_id: AppState.sessionId
        }));

        // Clear input
        chatInput.value = '';

        // Show typing indicator
        this.showTypingIndicator();
    },

    handleMessage(data) {
        this.hideTypingIndicator();

        switch (data.type) {
            case 'chat_response':
                this.addMessage('mai', data.message);
                AppState.conversationMemory.push({
                    type: 'mai',
                    content: data.message,
                    timestamp: new Date()
                });
                break;

            case 'session_ended':
                Utils.showNotification('Session ended. Follow-up emails sent.', 'success');
                break;

            case 'error':
                Utils.showNotification(data.message || 'An error occurred', 'error');
                break;

            case 'pong':
                // Handle ping/pong for connection keep-alive
                break;
        }
    },

    addMessage(sender, content) {
        const messagesContainer = document.getElementById('chatMessages');
        if (!messagesContainer) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;

        const timestamp = Utils.formatTimestamp();
        messageDiv.innerHTML = `
            <div class="message-content">${Utils.sanitizeHtml(content)}</div>
            <div class="message-time">${timestamp}</div>
        `;

        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        // Add animation
        messageDiv.style.opacity = '0';
        messageDiv.style.transform = 'translateY(20px)';
        setTimeout(() => {
            messageDiv.style.transition = 'all 0.3s ease';
            messageDiv.style.opacity = '1';
            messageDiv.style.transform = 'translateY(0)';
        }, 10);
    },

    showTypingIndicator() {
        const messagesContainer = document.getElementById('chatMessages');
        if (!messagesContainer) return;

        // Remove existing typing indicator
        this.hideTypingIndicator();

        const typingDiv = document.createElement('div');
        typingDiv.className = 'message mai typing-indicator';
        typingDiv.id = 'typingIndicator';
        typingDiv.innerHTML = `
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        `;

        messagesContainer.appendChild(typingDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    },

    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typingIndicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    },

    updateConnectionStatus(status) {
        // Update any connection status indicators
        const statusElements = document.querySelectorAll('.status-indicator');
        statusElements.forEach(el => {
            el.className = `status-indicator ${status}`;
            const statusText = el.querySelector('span:last-child');
            if (statusText) {
                statusText.textContent = status === 'connected' ? 'Connected to Mai' : 'Disconnected';
            }
        });
    }
};

// Voice Chat functionality
const VoiceChat = {
    mediaRecorder: null,
    audioChunks: [],
    isRecording: false,

    init() {
        this.setupEventListeners();
        this.checkMicrophonePermission();
    },

    setupEventListeners() {
        const startBtn = document.getElementById('startVoiceBtn');
        const stopBtn = document.getElementById('stopVoiceBtn');

        if (startBtn) {
            startBtn.addEventListener('click', () => this.start());
        }

        if (stopBtn) {
            stopBtn.addEventListener('click', () => this.stop());
        }
    },

    async checkMicrophonePermission() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            stream.getTracks().forEach(track => track.stop());
            this.updateStatus('ready', 'Microphone ready');
        } catch (error) {
            console.error('Microphone permission denied:', error);
            this.updateStatus('error', 'Microphone access denied');
            Utils.showNotification('Please allow microphone access for voice chat', 'warning');
        }
    },

    async start() {
        try {
            if (!AppState.sessionId) {
                AppState.sessionId = Utils.generateSessionId();
            }

            // Get microphone access
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            });

            // Setup media recorder
            this.mediaRecorder = new MediaRecorder(stream, {
                mimeType: 'audio/webm;codecs=opus'
            });

            this.audioChunks = [];

            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.audioChunks.push(event.data);
                }
            };

            this.mediaRecorder.onstop = () => {
                this.processAudioData();
            };

            // Start recording
            this.mediaRecorder.start(1000); // Collect data every second
            this.isRecording = true;

            // Update UI
            this.updateButtons(true);
            this.updateStatus('connected', 'Recording... Speak now');
            Utils.showNotification('Voice recording started', 'success');

            // Start voice session with backend
            await this.startVoiceSession();

        } catch (error) {
            console.error('Failed to start voice chat:', error);
            Utils.showNotification('Failed to start voice recording', 'error');
            this.updateStatus('error', 'Failed to start recording');
        }
    },

    stop() {
        if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.stop();
            this.isRecording = false;

            // Stop all tracks
            if (this.mediaRecorder.stream) {
                this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
            }

            // Update UI
            this.updateButtons(false);
            this.updateStatus('disconnected', 'Voice chat stopped');
            Utils.showNotification('Voice recording stopped', 'info');

            // End session
            this.endVoiceSession();
        }
    },

    async startVoiceSession() {
        try {
            const response = await Utils.apiCall('/voice/start', {
                method: 'POST',
                body: JSON.stringify({
                    webrtc_id: AppState.sessionId,
                    voice_name: 'Aoede',
                    mode: 'audio',
                    session_id: AppState.sessionId
                })
            });

            console.log('Voice session started:', response);
        } catch (error) {
            console.error('Failed to start voice session:', error);
        }
    },

    async endVoiceSession() {
        try {
            const response = await Utils.apiCall('/session/end', {
                method: 'POST',
                body: JSON.stringify({
                    session_id: AppState.sessionId,
                    reason: 'user_stopped'
                })
            });

            if (response.emails_sent > 0) {
                Utils.showNotification(`Session ended. ${response.emails_sent} follow-up emails sent.`, 'success');
            }
        } catch (error) {
            console.error('Failed to end voice session:', error);
        }
    },

    async processAudioData() {
        if (this.audioChunks.length === 0) return;

        try {
            const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });

            // For now, we'll convert to text using Web Speech API
            // In a full implementation, this would be sent to the backend
            this.convertSpeechToText(audioBlob);

        } catch (error) {
            console.error('Error processing audio:', error);
        }
    },

    convertSpeechToText(audioBlob) {
        // This is a simplified implementation
        // In production, you'd send the audio to your backend for processing
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            const recognition = new SpeechRecognition();

            recognition.continuous = false;
            recognition.interimResults = false;
            recognition.lang = 'en-US';

            recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                this.addVoiceMessage('user', `[Voice] ${transcript}`);

                // Send to text chat for processing
                this.sendVoiceMessageToBackend(transcript);
            };

            recognition.onerror = (event) => {
                console.error('Speech recognition error:', event.error);
                Utils.showNotification('Speech recognition failed', 'error');
            };

            recognition.start();
        } else {
            Utils.showNotification('Speech recognition not supported in this browser', 'warning');
        }
    },

    async sendVoiceMessageToBackend(transcript) {
        try {
            const response = await Utils.apiCall('/chat', {
                method: 'POST',
                body: JSON.stringify({
                    prompt: transcript,
                    session_id: AppState.sessionId
                })
            });

            this.addVoiceMessage('mai', response.response);

            // Convert response to speech
            this.textToSpeech(response.response);

        } catch (error) {
            console.error('Failed to send voice message:', error);
            Utils.showNotification('Failed to process voice message', 'error');
        }
    },

    textToSpeech(text) {
        if ('speechSynthesis' in window) {
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.rate = 0.9;
            utterance.pitch = 1.1;
            utterance.volume = 0.8;

            // Try to use a female voice
            const voices = speechSynthesis.getVoices();
            const femaleVoice = voices.find(voice =>
                voice.name.toLowerCase().includes('female') ||
                voice.name.toLowerCase().includes('zira') ||
                voice.name.toLowerCase().includes('hazel')
            );

            if (femaleVoice) {
                utterance.voice = femaleVoice;
            }

            speechSynthesis.speak(utterance);
        }
    },

    addVoiceMessage(sender, content) {
        const messagesContainer = document.getElementById('voiceMessages');
        if (!messagesContainer) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `voice-message ${sender}`;

        const timestamp = Utils.formatTimestamp();
        messageDiv.innerHTML = `
            <div class="message-content">${Utils.sanitizeHtml(content)}</div>
            <div class="message-time">${timestamp}</div>
        `;

        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    },

    updateButtons(isRecording) {
        const startBtn = document.getElementById('startVoiceBtn');
        const stopBtn = document.getElementById('stopVoiceBtn');

        if (startBtn) {
            startBtn.disabled = isRecording;
        }

        if (stopBtn) {
            stopBtn.disabled = !isRecording;
        }
    },

    updateStatus(status, message) {
        const statusElement = document.getElementById('voiceStatus');
        if (statusElement) {
            statusElement.className = `status-indicator ${status}`;
            const statusText = statusElement.querySelector('span:last-child');
            if (statusText) {
                statusText.textContent = message;
            }
        }
    }
};

// Video Chat functionality
const VideoChat = {
    localStream: null,
    remoteStream: null,
    peerConnection: null,
    isVideoActive: false,

    init() {
        this.setupEventListeners();
        this.checkCameraPermission();
    },

    setupEventListeners() {
        const startBtn = document.getElementById('startVideoBtn');
        const stopBtn = document.getElementById('stopVideoBtn');

        if (startBtn) {
            startBtn.addEventListener('click', () => this.start());
        }

        if (stopBtn) {
            stopBtn.addEventListener('click', () => this.stop());
        }
    },

    async checkCameraPermission() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                video: true,
                audio: true
            });
            stream.getTracks().forEach(track => track.stop());
            this.updateStatus('ready', 'Camera and microphone ready');
        } catch (error) {
            console.error('Camera/microphone permission denied:', error);
            this.updateStatus('error', 'Camera access denied');
            Utils.showNotification('Please allow camera and microphone access for video chat', 'warning');
        }
    },

    async start() {
        try {
            if (!AppState.sessionId) {
                AppState.sessionId = Utils.generateSessionId();
            }

            // Get user media
            this.localStream = await navigator.mediaDevices.getUserMedia({
                video: {
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    facingMode: 'user'
                },
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            });

            // Display local video
            const localVideo = document.getElementById('localVideo');
            if (localVideo) {
                localVideo.srcObject = this.localStream;
            }

            // Setup WebRTC peer connection
            await this.setupPeerConnection();

            // Update UI
            this.isVideoActive = true;
            this.updateButtons(true);
            this.updateStatus('connected', 'Video chat active');
            Utils.showNotification('Video chat started', 'success');

            // Start video session with backend
            await this.startVideoSession();

        } catch (error) {
            console.error('Failed to start video chat:', error);
            Utils.showNotification('Failed to start video chat', 'error');
            this.updateStatus('error', 'Failed to start video');
        }
    },

    async setupPeerConnection() {
        this.peerConnection = new RTCPeerConnection({
            iceServers: CONFIG.iceServers
        });

        // Add local stream to peer connection
        this.localStream.getTracks().forEach(track => {
            this.peerConnection.addTrack(track, this.localStream);
        });

        // Handle remote stream
        this.peerConnection.ontrack = (event) => {
            const remoteVideo = document.getElementById('remoteVideo');
            if (remoteVideo && event.streams[0]) {
                remoteVideo.srcObject = event.streams[0];
                this.remoteStream = event.streams[0];
            }
        };

        // Handle ICE candidates
        this.peerConnection.onicecandidate = (event) => {
            if (event.candidate) {
                // In a real implementation, send this to the signaling server
                console.log('ICE candidate:', event.candidate);
            }
        };

        // Handle connection state changes
        this.peerConnection.onconnectionstatechange = () => {
            console.log('Connection state:', this.peerConnection.connectionState);

            switch (this.peerConnection.connectionState) {
                case 'connected':
                    this.updateStatus('connected', 'Video call connected');
                    break;
                case 'disconnected':
                case 'failed':
                    this.updateStatus('error', 'Video call failed');
                    break;
            }
        };
    },

    async startVideoSession() {
        try {
            const response = await Utils.apiCall('/video/start', {
                method: 'POST',
                body: JSON.stringify({
                    webrtc_id: AppState.sessionId,
                    voice_name: 'Aoede',
                    enable_video: true,
                    session_id: AppState.sessionId
                })
            });

            console.log('Video session started:', response);
        } catch (error) {
            console.error('Failed to start video session:', error);
        }
    },

    stop() {
        // Stop local stream
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => track.stop());
            this.localStream = null;
        }

        // Close peer connection
        if (this.peerConnection) {
            this.peerConnection.close();
            this.peerConnection = null;
        }

        // Clear video elements
        const localVideo = document.getElementById('localVideo');
        const remoteVideo = document.getElementById('remoteVideo');

        if (localVideo) {
            localVideo.srcObject = null;
        }

        if (remoteVideo) {
            remoteVideo.srcObject = null;
        }

        // Update UI
        this.isVideoActive = false;
        this.updateButtons(false);
        this.updateStatus('disconnected', 'Video chat stopped');
        Utils.showNotification('Video chat stopped', 'info');

        // End session
        this.endVideoSession();
    },

    async endVideoSession() {
        try {
            const response = await Utils.apiCall('/session/end', {
                method: 'POST',
                body: JSON.stringify({
                    session_id: AppState.sessionId,
                    reason: 'user_stopped'
                })
            });

            if (response.emails_sent > 0) {
                Utils.showNotification(`Session ended. ${response.emails_sent} follow-up emails sent.`, 'success');
            }
        } catch (error) {
            console.error('Failed to end video session:', error);
        }
    },

    updateButtons(isActive) {
        const startBtn = document.getElementById('startVideoBtn');
        const stopBtn = document.getElementById('stopVideoBtn');

        if (startBtn) {
            startBtn.disabled = isActive;
        }

        if (stopBtn) {
            stopBtn.disabled = !isActive;
        }
    },

    updateStatus(status, message) {
        const statusElement = document.getElementById('videoStatus');
        if (statusElement) {
            statusElement.className = `status-indicator ${status}`;
            const statusText = statusElement.querySelector('span:last-child');
            if (statusText) {
                statusText.textContent = message;
            }
        }
    }
};

// Contact Form functionality
const ContactForm = {
    init() {
        this.setupEventListeners();
    },

    setupEventListeners() {
        const form = document.getElementById('contactFormElement');
        if (form) {
            form.addEventListener('submit', (e) => this.handleSubmit(e));
        }
    },

    async handleSubmit(event) {
        event.preventDefault();

        const formData = new FormData(event.target);
        const contactInfo = {
            name: formData.get('name'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            company: formData.get('company'),
            purpose: formData.get('purpose')
        };

        // Validate required fields
        if (!contactInfo.name || !contactInfo.email || !contactInfo.purpose) {
            Utils.showNotification('Please fill in all required fields', 'error');
            return;
        }

        // Show loading state
        const submitBtn = event.target.querySelector('.submit-btn');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Sending...';
        submitBtn.disabled = true;

        try {
            const response = await Utils.apiCall('/contact', {
                method: 'POST',
                body: JSON.stringify(contactInfo)
            });

            if (response.status === 'success') {
                Utils.showNotification(
                    `Thank you! Your inquiry has been submitted. ${response.emails_sent} confirmation emails sent.`,
                    'success'
                );

                // Reset form
                event.target.reset();

                // Store contact info
                AppState.contactInfo = contactInfo;

            } else {
                Utils.showNotification('Failed to submit inquiry. Please try again.', 'error');
            }

        } catch (error) {
            console.error('Contact form submission error:', error);
            Utils.showNotification('Failed to submit inquiry. Please try again.', 'error');
        } finally {
            // Restore button
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    }
};

// Application initialization
class MaiVoiceAgent {
    constructor() {
        this.initialized = false;
    }

    async init() {
        if (this.initialized) return;

        try {
            // Initialize mode manager
            ModeManager.init();

            // Initialize default mode (text chat)
            TextChat.init();

            // Setup global event listeners
            this.setupGlobalEventListeners();

            // Check API health
            await this.checkApiHealth();

            this.initialized = true;
            console.log('Mai Voice Agent initialized successfully');

        } catch (error) {
            console.error('Failed to initialize Mai Voice Agent:', error);
            Utils.showNotification('Failed to initialize application', 'error');
        }
    }

    setupGlobalEventListeners() {
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // Page is hidden, pause activities
                this.pauseActivities();
            } else {
                // Page is visible, resume activities
                this.resumeActivities();
            }
        });

        // Handle beforeunload to clean up sessions
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });

        // Handle keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Enter to send message in chat mode
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter' && AppState.currentMode === 'chat') {
                TextChat.sendMessage();
            }
        });
    }

    async checkApiHealth() {
        try {
            const response = await Utils.apiCall('/health');
            if (response.status === 'healthy') {
                Utils.showNotification('Connected to Mai Voice Agent', 'success');
            }
        } catch (error) {
            console.error('API health check failed:', error);
            Utils.showNotification('Warning: API connection issues detected', 'warning');
        }
    }

    pauseActivities() {
        // Pause any ongoing activities when page is hidden
        if (AppState.currentMode === 'voice' && VoiceChat.isRecording) {
            VoiceChat.stop();
        }

        if (AppState.currentMode === 'video' && VideoChat.isVideoActive) {
            VideoChat.stop();
        }
    }

    resumeActivities() {
        // Resume activities when page becomes visible
        // For now, just update connection status
        if (AppState.currentMode === 'chat' && TextChat.websocket) {
            TextChat.websocket.send(JSON.stringify({ type: 'ping' }));
        }
    }

    cleanup() {
        // Clean up all active connections and sessions
        try {
            if (TextChat.websocket) {
                TextChat.disconnect();
            }

            if (VoiceChat.isRecording) {
                VoiceChat.stop();
            }

            if (VideoChat.isVideoActive) {
                VideoChat.stop();
            }
        } catch (error) {
            console.error('Cleanup error:', error);
        }
    }
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    const app = new MaiVoiceAgent();
    await app.init();
});

// Export for global access
window.MaiVoiceAgent = {
    AppState,
    Utils,
    ModeManager,
    TextChat,
    VoiceChat,
    VideoChat,
    ContactForm
};

    initializeEventListeners() {
        // Mode selector buttons
        document.querySelectorAll('.mode-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchMode(e.target.dataset.mode);
            });
        });

        // Chat functionality
        const chatInput = document.getElementById('chatInput');
        const sendBtn = document.getElementById('sendBtn');
        
        sendBtn.addEventListener('click', () => this.sendChatMessage());
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendChatMessage();
            }
        });

        // Voice functionality
        document.getElementById('startVoiceBtn').addEventListener('click', () => this.startVoiceChat());
        document.getElementById('stopVoiceBtn').addEventListener('click', () => this.stopVoiceChat());

        // Contact form
        document.getElementById('contactFormElement').addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitContactForm();
        });
    }

    initializeWebSocket() {
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = () => {
                console.log('WebSocket connected');
            };
            
            this.websocket.onmessage = (event) => {
                this.addChatMessage(event.data, 'mai');
            };
            
            this.websocket.onclose = () => {
                console.log('WebSocket disconnected');
                // Attempt to reconnect after 3 seconds
                setTimeout(() => this.initializeWebSocket(), 3000);
            };
            
            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
            };
        } catch (error) {
            console.error('Failed to initialize WebSocket:', error);
        }
    }

    loadMaiAvatar() {
        // Set a placeholder avatar for Mai
        const avatar = document.getElementById('maiAvatar');
        // You can replace this with the actual Mai GIF URL when available
        avatar.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxjaXJjbGUgY3g9IjYwIiBjeT0iNjAiIHI9IjYwIiBmaWxsPSJ1cmwoI2dyYWRpZW50KSIvPgo8dGV4dCB4PSI2MCIgeT0iNzAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSI0OCIgZm9udC13ZWlnaHQ9ImJvbGQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5NPC90ZXh0Pgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJncmFkaWVudCIgeDE9IjAiIHkxPSIwIiB4Mj0iMTIwIiB5Mj0iMTIwIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiM2MzY2ZjEiLz4KPHN0b3Agb2Zmc2V0PSI1MCUiIHN0b3AtY29sb3I9IiM4YjVjZjYiLz4KPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDZiNmQ0Ii8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPC9zdmc+';
    }

    switchMode(mode) {
        // Update active button
        document.querySelectorAll('.mode-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-mode="${mode}"]`).classList.add('active');

        // Hide all interfaces
        document.querySelectorAll('.chat-interface, .voice-interface, .contact-form').forEach(el => {
            el.classList.remove('active');
        });

        // Show selected interface
        switch(mode) {
            case 'chat':
                document.getElementById('chatInterface').classList.add('active');
                break;
            case 'voice':
                document.getElementById('voiceInterface').classList.add('active');
                break;
            case 'contact':
                document.getElementById('contactForm').classList.add('active');
                break;
        }

        this.currentMode = mode;
    }

    async sendChatMessage() {
        const input = document.getElementById('chatInput');
        const message = input.value.trim();
        
        if (!message) return;

        // Add user message to chat
        this.addChatMessage(message, 'user');
        input.value = '';

        try {
            if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                // Send via WebSocket for real-time response
                this.websocket.send(message);
            } else {
                // Fallback to HTTP API
                const response = await fetch('/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: message,
                        temperature: 0.7,
                        max_tokens: 512
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    this.addChatMessage(data.response, 'mai');
                } else {
                    throw new Error('Failed to get response');
                }
            }
        } catch (error) {
            console.error('Error sending message:', error);
            this.addChatMessage('I apologize, but I\'m having trouble connecting right now. Please try again or use the contact form.', 'mai');
        }
    }

    addChatMessage(message, sender) {
        const messagesContainer = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;
        messageDiv.textContent = message;
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    async startVoiceChat() {
        try {
            this.updateVoiceStatus('connecting', 'Connecting to voice chat...');
            
            // Check if WebRTC is available
            const statusResponse = await fetch('/audio/webrtc/status');
            const statusData = await statusResponse.json();
            
            if (!statusData.available) {
                throw new Error('Voice chat is not available on this server');
            }

            // Initialize WebRTC connection
            await this.initializeWebRTC();
            
            this.isVoiceActive = true;
            document.getElementById('startVoiceBtn').disabled = true;
            document.getElementById('stopVoiceBtn').disabled = false;
            
            this.updateVoiceStatus('connected', 'Voice chat active - speak now');
            
        } catch (error) {
            console.error('Error starting voice chat:', error);
            this.updateVoiceStatus('disconnected', 'Voice chat unavailable');
            this.addVoiceMessage('Voice chat is currently unavailable. Please use text chat instead.', 'system');
        }
    }

    async stopVoiceChat() {
        try {
            if (this.webrtcConnection) {
                this.webrtcConnection.close();
                this.webrtcConnection = null;
            }
            
            this.isVoiceActive = false;
            document.getElementById('startVoiceBtn').disabled = false;
            document.getElementById('stopVoiceBtn').disabled = true;
            
            this.updateVoiceStatus('disconnected', 'Voice chat stopped');
            
        } catch (error) {
            console.error('Error stopping voice chat:', error);
        }
    }

    async initializeWebRTC() {
        // This is a simplified WebRTC setup
        // In a full implementation, you would need to handle the complete WebRTC flow
        try {
            const response = await fetch('/input_hook', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    webrtc_id: 'mai_' + Date.now(),
                    voice_name: 'Aoede',
                    mode: 'audio'
                })
            });

            if (!response.ok) {
                throw new Error('Failed to initialize voice connection');
            }

            const data = await response.json();
            console.log('Voice connection initialized:', data);
            
        } catch (error) {
            console.error('WebRTC initialization error:', error);
            throw error;
        }
    }

    updateVoiceStatus(status, message) {
        const statusElement = document.getElementById('voiceStatus');
        statusElement.className = `status-indicator ${status}`;
        statusElement.querySelector('span:last-child').textContent = message;
    }

    addVoiceMessage(message, sender) {
        const messagesContainer = document.getElementById('voiceMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;
        messageDiv.style.marginBottom = '1rem';
        messageDiv.style.padding = '0.75rem 1rem';
        messageDiv.style.borderRadius = '0.75rem';
        
        if (sender === 'system') {
            messageDiv.style.background = 'rgba(245, 158, 11, 0.1)';
            messageDiv.style.border = '1px solid rgba(245, 158, 11, 0.3)';
            messageDiv.style.color = '#f59e0b';
        }
        
        messageDiv.textContent = message;
        messagesContainer.appendChild(messageDiv);
    }

    async submitContactForm() {
        const form = document.getElementById('contactFormElement');
        const formData = new FormData(form);
        
        const contactData = {
            name: formData.get('name'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            company: formData.get('company'),
            purpose: formData.get('purpose')
        };

        try {
            const response = await fetch('/submit_contact', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(contactData)
            });

            if (response.ok) {
                const result = await response.json();
                alert('Thank you! We\'ve received your information and will be in touch soon.');
                form.reset();
            } else {
                throw new Error('Failed to submit contact form');
            }
        } catch (error) {
            console.error('Error submitting contact form:', error);
            alert('There was an error submitting your information. Please try again or contact us <NAME_EMAIL>');
        }
    }
}

// Initialize the Mai Voice Agent when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new MaiVoiceAgent();
});
