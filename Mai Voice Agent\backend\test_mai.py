#!/usr/bin/env python3
"""
Test script for Mai Voice Agent
Run this to verify basic functionality before deployment
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

async def test_imports():
    """Test that all required imports work"""
    print("🧪 Testing imports...")
    
    try:
        import fastapi
        print("✅ FastAPI imported successfully")
    except ImportError as e:
        print(f"❌ FastAPI import failed: {e}")
        return False
    
    try:
        import uvicorn
        print("✅ Uvicorn imported successfully")
    except ImportError as e:
        print(f"❌ Uvicorn import failed: {e}")
        return False
    
    try:
        from google import genai
        print("✅ Google Genai imported successfully")
    except ImportError as e:
        print(f"❌ Google Genai import failed: {e}")
        return False
    
    try:
        import numpy as np
        print("✅ NumPy imported successfully")
    except ImportError as e:
        print(f"❌ NumPy import failed: {e}")
        return False
    
    try:
        import smtplib
        print("✅ SMTP library available")
    except ImportError as e:
        print(f"❌ SMTP library import failed: {e}")
        return False
    
    return True

async def test_environment():
    """Test environment configuration"""
    print("\n🔧 Testing environment configuration...")
    
    # Check for .env file
    env_file = Path(__file__).parent.parent / ".env"
    if env_file.exists():
        print("✅ .env file found")
    else:
        print("⚠️  .env file not found (using environment variables)")
    
    # Check critical environment variables
    gemini_key = os.getenv("GEMINI_API_KEY")
    if gemini_key:
        print("✅ GEMINI_API_KEY is set")
    else:
        print("❌ GEMINI_API_KEY is not set")
        return False
    
    email_address = os.getenv("EMAIL_ADDRESS", "<EMAIL>")
    email_password = os.getenv("EMAIL_PASSWORD", "lceg dmyy fvwm fkor")
    
    if email_address and email_password:
        print("✅ Email configuration is set")
    else:
        print("❌ Email configuration is incomplete")
        return False
    
    return True

async def test_mai_functionality():
    """Test Mai's core functionality"""
    print("\n🤖 Testing Mai functionality...")
    
    try:
        # Import main module
        from main import (
            SYSTEM_PROMPT, 
            detect_goodbye, 
            extract_contact_info,
            send_email
        )
        print("✅ Main module imported successfully")
        
        # Test system prompt
        if "Mai" in SYSTEM_PROMPT and "Critical Future" in SYSTEM_PROMPT:
            print("✅ System prompt contains Mai and Critical Future")
        else:
            print("❌ System prompt is missing key elements")
            return False
        
        # Test goodbye detection
        if detect_goodbye("goodbye"):
            print("✅ Goodbye detection works")
        else:
            print("❌ Goodbye detection failed")
            return False
        
        # Test contact info extraction
        test_text = "My name is John Doe and my <NAME_EMAIL>"
        contact_info = extract_contact_info(test_text)
        if contact_info.get("name") and contact_info.get("email"):
            print("✅ Contact info extraction works")
        else:
            print("❌ Contact info extraction failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Mai functionality test failed: {e}")
        return False

async def test_fastapi_app():
    """Test FastAPI application setup"""
    print("\n🌐 Testing FastAPI application...")
    
    try:
        from main import app
        print("✅ FastAPI app created successfully")
        
        # Check if routes are registered
        routes = [route.path for route in app.routes]
        expected_routes = ["/", "/health", "/chat", "/submit_contact"]
        
        for route in expected_routes:
            if route in routes:
                print(f"✅ Route {route} is registered")
            else:
                print(f"❌ Route {route} is missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ FastAPI app test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 Starting Mai Voice Agent Tests\n")
    
    tests = [
        ("Imports", test_imports),
        ("Environment", test_environment),
        ("Mai Functionality", test_mai_functionality),
        ("FastAPI App", test_fastapi_app),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Mai Voice Agent is ready for deployment.")
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix issues before deployment.")
        return False

if __name__ == "__main__":
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("⚠️  python-dotenv not available, using system environment variables")
    
    # Run tests
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
