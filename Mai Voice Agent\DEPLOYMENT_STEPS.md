# Mai Voice Agent - Railway Deployment Steps

## 🚀 Quick Deployment Guide

Follow these exact steps to deploy Mai Voice Agent to Railway:

### Step 1: Prepare GitHub Repository

```bash
# Navigate to the Mai Voice Agent directory
cd "Mai Voice Agent"

# Initialize git repository
git init

# Add all files
git add .

# Commit with initial message
git commit -m "Initial Mai Voice Agent setup for Critical Future"

# Create GitHub repository (do this on GitHub.com first)
# Then add remote origin
git remote add origin https://github.com/yourusername/mai-voice-agent.git

# Push to GitHub
git push -u origin main
```

### Step 2: Deploy to Railway

1. **Go to Railway**
   - Visit [railway.app](https://railway.app)
   - Sign in or create account

2. **Create New Project**
   - Click "New Project"
   - Select "Deploy from GitHub repo"
   - Choose your `mai-voice-agent` repository
   - Railway will automatically detect the Dockerfile

3. **Wait for Initial Build**
   - Railway will start building automatically
   - This may take 5-10 minutes for first build

### Step 3: Configure Environment Variables

In Railway dashboard, go to your project → **Variables** tab and add these **EXACT** variables:

```
GEMINI_API_KEY=your_actual_gemini_api_key_here
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=lceg dmyy fvwm fkor
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
PORT=8000
USE_VERTEX_AI=false
```

**Important**: Replace `your_actual_gemini_api_key_here` with your real Gemini API key.

### Step 4: Redeploy with Environment Variables

After adding environment variables:
1. Go to **Deployments** tab
2. Click **Deploy** button to trigger new deployment
3. Wait for build to complete

### Step 5: Test Your Deployment

1. **Get Your URL**
   - In Railway dashboard, find your app URL (usually `https://your-app-name.railway.app`)

2. **Test Health Check**
   - Visit: `https://your-app-name.railway.app/health`
   - Should return JSON with status "healthy"

3. **Test Main Interface**
   - Visit: `https://your-app-name.railway.app`
   - Should show Mai's interface

4. **Test Text Chat**
   - Use the chat interface to talk with Mai
   - Verify responses are working

5. **Test Contact Form**
   - Fill out the contact form
   - Check if emails are sent successfully

### Step 6: Custom Domain (Optional)

1. In Railway dashboard → **Settings** → **Domains**
2. Add your custom domain
3. Update DNS records as instructed by Railway

## 🔧 Environment Variables Explained

| Variable | Value | Purpose |
|----------|-------|---------|
| `GEMINI_API_KEY` | Your API key | Enables AI conversations |
| `EMAIL_ADDRESS` | <EMAIL> | Sender email for follow-ups |
| `EMAIL_PASSWORD` | lceg dmyy fvwm fkor | Gmail app password |
| `SMTP_SERVER` | smtp.gmail.com | Email server |
| `SMTP_PORT` | 587 | Email server port |
| `PORT` | 8000 | Web server port |

## 🐛 Troubleshooting

### Build Fails
- Check **Deployments** → **Build Logs** for errors
- Ensure all files are committed to GitHub
- Verify Dockerfile syntax

### App Doesn't Start
- Check **Deployments** → **Deploy Logs**
- Verify environment variables are set correctly
- Ensure `GEMINI_API_KEY` is valid

### Email Not Working
- Verify Gmail app password is correct
- Check if 2FA is enabled on Gmail account
- Test email credentials manually

### Voice Chat Issues
- Voice features may be limited in cloud deployment
- Text chat should always work
- Check browser microphone permissions

## ✅ Success Checklist

- [ ] Repository created on GitHub
- [ ] Railway project deployed successfully
- [ ] Environment variables configured
- [ ] Health check returns "healthy"
- [ ] Main interface loads correctly
- [ ] Text chat responds to messages
- [ ] Contact form submits successfully
- [ ] Emails are being sent

## 🎯 Final Result

Once deployed successfully, you'll have:

- **Live URL**: `https://your-app-name.railway.app`
- **Mai Interface**: Beautiful dark-themed chat interface
- **AI Conversations**: Powered by Google Gemini with Aoede voice
- **Email Follow-ups**: Automatic emails to customers and Critical Future team
- **Contact Forms**: Structured inquiry collection
- **Health Monitoring**: Built-in health checks and debug endpoints

Your Mai Voice Agent is now ready to serve Critical Future's customers! 🎉

## 📞 Support

If you encounter issues:
- Check Railway deployment logs
- Visit `/debug` endpoint for diagnostics
- Contact: <EMAIL>
